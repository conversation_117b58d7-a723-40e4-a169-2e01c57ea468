import { override } from "@microsoft/decorators";
import { BaseApplicationCustomizer } from "@microsoft/sp-application-base";
import enUS from "./locales/en-us";
import jaJp from "./locales/ja-jp";

type LocaleStrings = typeof enUS;

const localeMap: Record<"en" | "jp", LocaleStrings> = {
  en: enUS,
  jp: jaJp,
};

export default class PlatonSiteCustomizationApplicationCustomizer extends BaseApplicationCustomizer<{}> {
  private toggle: HTMLElement | null = null;
  private labelEN: HTMLElement | null = null;
  private labelJP: HTMLElement | null = null;

  @override
  public onInit(): Promise<void> {
    const currentUrl = window.location.href;

    const currentLang = localStorage.getItem("platonLang") || "en";
    const setLang = (lang: string): void => {
      localStorage.setItem("platonLang", lang);
      this.toggle?.classList.remove("en", "jp");
      this.toggle?.classList.add(lang);
      this.labelEN?.classList.toggle("active", lang === "en");
      this.labelJP?.classList.toggle("active", lang === "jp");
      applyTranslations(lang as "en" | "jp");
    };

    // Define all target page paths in an array
    const targetPagePaths = [
      "/sites/ProjectEngLand/SitePages/Home-Pages1.aspx",
      "/sites/ProjectEngLand/SitePages/YetAnotherPage.aspx",
      "https://corptb.sharepoint.com/sites/TMTMFUSOMIGRATION/SitePages/Platon.aspx",
    ];

    // Check if the current URL includes any of the target paths
    const isTargetPage = targetPagePaths.some((path) =>
      currentUrl.includes(path)
    );

    if (!isTargetPage) {
      return Promise.resolve(); // Exit early if the current URL does not match any of the target pages
    }

    // ✅ Continue only if on the target page
    console.log("App Customizer is running on the correct page.");
    const elementsToHide = [
      "spSiteHeader",
      "spCommandBar",
      "spLeftNav",
      "sp-appBar",
      "appBar",
      "CommentsWrapper",
      "SuiteNavWrapper",
    ];

    elementsToHide.forEach((id) => {
      const element = document.getElementById(id);
      if (element) {
        element.style.display = "none";
      }
    });

    const NAV_ID = "customTopNav";
    if (document.getElementById(NAV_ID)) {
      return Promise.resolve();
    }

    // Inject Font Awesome
    if (!document.querySelector('link[href*="font-awesome"]')) {
      const fa = document.createElement("link");
      fa.rel = "stylesheet";
      fa.href =
        "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css";
      document.head.appendChild(fa);
    }

    setTimeout(() => {
      const searchInput = document.getElementById(
        "spSearchInput"
      ) as HTMLInputElement;
      const searchButton = document.querySelector(
        ".search-button"
      ) as HTMLButtonElement;

      const performSearch = (): void => {
        const input = document.getElementById(
          "spSearchInput"
        ) as HTMLInputElement;
        const query = input.value.trim(); // e.g., from a text input
        if (query) {
          window.history.pushState({}, "", `?q=${encodeURIComponent(query)}`);
          window.dispatchEvent(
            new CustomEvent("platonSearchChanged", { detail: query })
          );
        }
      };

      if (searchInput) {
        searchInput.addEventListener("keypress", (event: KeyboardEvent) => {
          if (event.key === "Enter") {
            performSearch();
          }
        });
      }

      if (searchButton) {
        searchButton.addEventListener("click", () => {
          performSearch();
        });
      }

      const clearButton = document.getElementById(
        "clearSearchBtn"
      ) as HTMLButtonElement;

      if (clearButton) {
        clearButton.addEventListener("click", () => {
          if (searchInput) searchInput.value = "";
          window.history.pushState({}, "", window.location.pathname); // Reset ?q= from URL
          window.dispatchEvent(
            new CustomEvent("platonSearchChanged", { detail: "" })
          );
        });
      }
    }, 200);

    setTimeout(() => {
      const toggleBtn = document.getElementById("chatToggleBtn");
      const closeBtn = document.getElementById("chatCloseBtn");
      const chatBox = document.getElementById("chatContainer");
      const sendMessageBtn = document.getElementById("sendMessageBtn");
      const chatInput = document.getElementById(
        "chatInput"
      ) as HTMLInputElement;

      if (toggleBtn && closeBtn && chatBox) {
        toggleBtn.addEventListener("click", () => {
          chatBox.classList.toggle("hidden");
        });

        closeBtn.addEventListener("click", () => {
          chatBox.classList.add("hidden");
        });

        sendMessageBtn?.addEventListener("click", () => {
          sendChatMessage(chatInput);
        });

        chatInput?.addEventListener("keypress", (event: KeyboardEvent) => {
          if (event.key === "Enter") {
            sendChatMessage(chatInput);
          }
        });
      }

      function sendChatMessage(inputElement: HTMLInputElement): void {
        if (inputElement && inputElement.value.trim() !== "") {
          const message = inputElement.value.trim();
          const iframe = document.querySelector(
            ".chatbot-frame"
          ) as HTMLIFrameElement;

          if (iframe) {
            iframe.contentWindow?.postMessage({ message }, "*");
          }

          inputElement.value = ""; // Clear the input box after sending
        }
      }
    }, 300);

    // Inject styles for top navigation and side navigation
    const style = document.createElement("style");
    style.textContent = `
      body {
        margin: 0;
        font-family: Arial, Helvetica, sans-serif;
        scrollbar-gutter: stable;
      }

      *, *::before, *::after {
        box-sizing: border-box;
      }

      .topnav {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: white;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        width: 100%;
        z-index: 9999;
        height: 60px;
        padding: 0 20px;
        box-shadow: 0px 5px 8px grey;
        box-sizing: border-box;
      }

      .nav-left .nav-logo {
        height: 40px;
      }

      .nav-right {
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .nav-right a {
        color: black;
        text-align: center;
        padding: 0 15px;
        text-decoration: none;
        font-size: 15px;
        line-height: 60px;
      }

      .nav-right a:hover {
        background-color: lightgrey;
        color: white;
      }

      .icon {
        display: none;
        font-size: 22px;
        color: black;
        cursor: pointer;
      }

      @media screen and (max-width: 768px) {
        .topnav {
          flex-wrap: wrap;
          height: auto;
          padding: 10px;
        }

        .nav-left {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .nav-center {
          width: 100%;
          margin-top: 10px;
          display: flex;
          justify-content: center;
        }

        .search-input {
          width: 100%;
          max-width: none;
        }

        .search-container {
          width: 100%;
        }

        .lang-toggle {
          display: flex;
          gap: 6px;
          background: #f1f1f1;
          border-radius: 20px;
          padding: 4px;
          margin-left: 20px;
        }

        .lang-btn {
          border: none;
          padding: 6px 12px;
          border-radius: 20px;
          font-size: 13px;
          cursor: pointer;
          background-color: transparent;
          transition: background-color 0.3s ease;
        }

        .lang-btn.active {
          background-color: #0078d4;
          color: white;
        }

        .lang-btn:hover:not(.active) {
          background-color: #ddd;
        }

        .user-profile-wrapper {
          margin-top: 10px;
          width: 100%;
          justify-content: flex-start;
        }

        .icon {
          display: block;
          font-size: 22px;
          color: black;
          cursor: pointer;
        }

        .nav-right {
          flex-direction: column;
          width: 100%;
          align-items: flex-start;
          padding-top: 10px;
          background-color: white;
          display: none;
        }

        .topnav.responsive .nav-right {
          display: flex;
        }

        .topnav.responsive .nav-right a {
          display: block;
          width: 100%;
          text-align: left;
          padding: 10px 20px;
          line-height: 1.5;
        }

        .user-avatar {
          width: 28px;
          height: 28px;
        }
      }

      .nav-center {
        flex-grow: 1;
        display: flex;
        justify-content: center;
      }

      .search-container {
        position: relative;
        display: flex;
        align-items: center;
      }

      .search-container i {
        position: absolute;
        left: 15px;
        color: white;
        font-size: 14px;
      }

      .search-input {
        padding: 10px 10px 10px 30px;
        font-size: 14px;
        border-radius: 20px;
        border: 1px solid #ccc;
        width: 350px;
        transition: all 0.3s ease;
      }

      .search-input:focus {
        outline: none;
        border: 1px solid transparent;
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
      }

      .search-button {
        position: absolute;
        top: 50%;
        right: 0px;
        transform: translateY(-50%);
        padding: 20px;
        background-color: #0078d4;
        color: white;
        border: none;
        border-radius: 0px 20px 20px 0px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 32px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      .user-info {
        font-size: 14px;
        color: #333;
        white-space: nowrap;
      }

      .user-profile {
        display: flex;
        align-items: center;
        margin-left: 10px;
      }

      .user-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        object-fit: cover;
        border: 1px solid #ccc;
      }

      .user-profile-wrapper {
        display: flex;
        align-items: center;
        background-color: #d3d3d326;
        padding: 5px 10px;
        border-radius: 8px;
        margin-left: 10px;
        gap: 8px;
      }

      .settings {
        color: #0078d4;
        background: #80808014;
        padding: 12px;
        border-radius: 20px;
        box-shadow: 1px 2px 8px #80808091;
      }

      /* Side navigation styles */
      .custom-side-nav { 
        position: fixed; 
        top: 60px; 
        left: 0; 
        height: calc(100vh - 60px); 
        width: 60px; 
        background: white; 
        z-index: 10000; 
        transition: left 0.3s ease, width 0.3s ease;
      }

      .side-toggle-icon {
        position: absolute;
        top: 10px;
        right: -15px;
        background: white;
        border-radius: 50%;
        box-shadow: 0 2px 6px rgba(0,0,0,0.3);
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 10000;
        transition: transform 0.3s ease;
      }

      .side-toggle-icon i {
        transition: transform 0.3s ease;
      }

      .custom-side-nav.expanded .side-toggle-icon i {
        transform: rotate(180deg);
      }

      div#sideNavToggle i {
        margin: 0px;
        color : grey;
      }

      .custom-side-nav.expanded .side-toggle-icon i {
        transform: rotate(180deg);
      }

      #workbenchPageContent,
      .CanvasZone,
      .ControlZone,
      div[class*="ControlZone"],
      div[class*="CanvasZone"],
      .section-front-page-banner {
      margin-left: 10px !important; /* match your sidebar width */
      transition: margin-left 0.3s ease;
      }

      .custom-side-nav.expanded { 
        width: 280px; 
      }

      .custom-side-nav ul { 
        list-style: none; 
        padding: 0; 
        margin: 0; 
        overflow-y: hidden;
        overflow-x: hidden;
        height: 100%;
      }

      .custom-side-nav ul:hover {
        overflow-y: scroll;
      }
        
      .custom-side-nav {
        scrollbar-gutter: stable;
      }

      .custom-side-nav li { 
        padding: 10px; 
        color: grey; 
        height: 50px; 
        cursor: pointer; 
      }
        .custom-side-nav li a { 
        text-decoration:none;
        color:grey;
      }
        

      .custom-side-nav i { 
        margin-right: 10px; 
        font-size: 18px; 
        width: 24px; 
        text-align: center; 
      }

      .custom-side-nav span { 
        opacity: 0; 
      }

      .custom-side-nav.expanded span { 
        opacity: 1; 
        transition-delay: 0.2s;
      }

      body.nav-expanded {
        margin-left: 0px; /* or add padding-left to the main content */
        transition: margin-left 0.3s ease; /* Smooth transition */
      }

      @media (max-width: 768px) {
        .custom-side-nav {
          top: 0;
          left: -200px;
          width: 60px;
        }

        .custom-side-nav.expanded {
          left: 0;
          width: 200px;
        }
      }
    `;
    document.head.appendChild(style);

    // Create side navigation HTML
    const sideNav = document.createElement("div");
    sideNav.className = "custom-side-nav";
    sideNav.innerHTML = `
      <div id="sideNavToggle" class="side-toggle-icon">
        <i class="fa fa-angle-double-right" aria-hidden="true"></i>
      </div>
      <ul>
        <li><a href="https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/Home-Pages1.aspx"><i class="fas fa-home"></i><span data-i18n-key="HomeLabel"> Home </span></a></li>
        <li><a href="https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/PlatonStartingPoint.aspx"><i class="fa fa-hourglass-start"></i><span data-i18n-key='StartingPointLabel'> Starting Point </span></a></li>
        <li data-scroll-target="short-trips"><a href="https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/Home-Pages1.aspx"><i class="fas fa-atlas"></i><span data-i18n-key='ShortTripsLabel'> PLATON Short Trips</span></a></li>
        <li data-scroll-target="depth-session"><a href="https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/Home-Pages1.aspx"><i class="fas fa-chalkboard-teacher"></i><span data-i18n-key='DepthSessionLabel'> PLATON Depth Session</span></a></li>
        <li data-scroll-target="module-series"><a href="https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/Home-Pages1.aspx"><i class="fa fa-puzzle-piece"></i><span data-i18n-key='ModuleSeriesLabel'>PLATON Module Series</span></a></li>
        <li data-scroll-target="manuals"><a href="https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/Home-Pages1.aspx"><i class="fa fa-file-alt"></i><span data-i18n-key='ManualsLabel'>PLATON Manuals</span></a></li>
        <li><a href="https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/BlockYourTraining(1).aspx"><i class="fa fa-check-square"></i><span data-i18n-key='BlockTrainingLabel'>  BLOCK your Training</span></a></li>
        <li data-scroll-target="system-demos"><a href="https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/Home-Pages1.aspx"><i class="fa fa-unlock"></i><span data-i18n-key='AccessPlatonLabel'> Access PLATON </span></a></li>
        <li><a href="https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/KeyUsersInfo.aspx"><i class="fa fa-user-tie"></i><span data-i18n-key='KeyUsersLabel'> Key Users Info </span></a></li>
        <li><a href="https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/Gallery.aspx"><i class="fa fa-chart-line"></i><span data-i18n-key='PeakCaptureLabel'> Peak Capture</span></a></li>
        <li><a href="https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/PROJECT-ENGLAND(2).aspx"><i class="fas fa-flag"></i><span data-i18n-key='ProjectEnglandLabel'>Project England</span></a></li>
        <li><a href="https://corptb.sharepoint.com/sites/ProjectEngLand/SitePages/ContactUs.aspx"><i class="fa fa-envelope"></i><span data-i18n-key='ContactUsLabel'>Contact Us</span></a></li>
      </ul>
    `;
    document.body.appendChild(sideNav);

    setTimeout(() => {
      const menuItems = document.querySelectorAll("[data-scroll-target]");

      menuItems.forEach((item) => {
        item.addEventListener("click", (e) => {
          e.preventDefault(); // Prevent any default anchor behavior
          const targetId = item.getAttribute("data-scroll-target");
          if (targetId) {
            const section = document.getElementById(targetId);
            if (section) {
              section.scrollIntoView({ behavior: "smooth", block: "start" });
            }
          }
        });
      });
    }, 500); // Delay ensures web part sections are rendered

    setTimeout(() => {
      const toggleIcon = document.getElementById("sideNavToggle");
      if (toggleIcon) {
        toggleIcon.addEventListener("click", () => {
          sideNav.classList.toggle("expanded");
          document.body.classList.toggle("nav-expanded");
        });
      }
    }, 50); // Ensure the element is mounted

    // Inject top navigation HTML
    const nav = document.createElement("div");
    nav.id = NAV_ID;
    nav.innerHTML = `
      <div class="topnav" id="myTopnav">
        <div class="nav-left">
          <img src="https://corptb.sharepoint.com/sites/ProjectEngLand/_api/siteiconmanager/getsitelogo?type='1'&hash=638792923750345522" alt="Logo" class="nav-logo" />
        </div>
        <div class="nav-center">
          <div class="search-container">
            <button type="submit" class="search-button">
              <i class="fas fa-search" style="font-size: 14px;"></i>
            </button>
            <input type="text" id="spSearchInput" placeholder="What do you want to learn?" class="search-input" />
            <input type="button" id=clearSearchBtn" class="clear-button" title="Clear">&times;</button>
          </div>
        </div>
        <div class="lang-flag-toggle">
          <span id="labelEN" class="lang-flag-label">EN</span>
          <div id="langToggle" class="lang-flag-switch">
            <div class="circle"></div>
          </div>
          <span id="labelJP" class="lang-flag-label">JP</span>
        </div>
        <a href="${this.context.pageContext.web.absoluteUrl}/_layouts/15/settings.aspx" target="_blank"><i class="fas fa-cog settings"></i></a>
        <div class="user-profile-wrapper">
          <div class="user-profile" id="userProfile"></div>
          <div class="user-info" id="userInfo"></div>
        </div>
      </div>
    `;
    document.body.insertBefore(nav, document.body.firstChild);

    const toggleStyle = document.createElement("style");
    toggleStyle.textContent = `
    .clear-button {
  position: absolute;
  right: 60px;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  font-size: 18px;
  color: #888;
  cursor: pointer;
}

.clear-button:hover {
  color: #333;
}

      .lang-flag-toggle {
        display: flex;
        align-items: center;
        border-radius: 30px;
        padding: 5px 8px;
        width: fit-content;
        gap: 8px;
        margin-right: 15px;
      }
      .lang-flag-label {
        font-size: 14px;
        font-weight: 600;
        color: #888;
        transition: color 0.3s ease;
      }
      .lang-flag-label.active {
        color: #333;
      }
      .lang-flag-switch {
        position: relative;
        width: 46px;
        height: 26px;
        background: #ddd;
        border-radius: 26px;
        cursor: pointer;
      }
      .lang-flag-switch .circle {
        position: absolute;
        top: 2px;
        left: 2px;
        width: 22px;
        height: 22px;
        background-color: white;
        background-size: cover;
        background-position: center;
        border-radius: 50%;
        transition: transform 0.3s ease;
      }
      .lang-flag-switch.jp .circle {
        transform: translateX(20px);
        background-image: url('https://upload.wikimedia.org/wikipedia/en/9/9e/Flag_of_Japan.svg');
      }
      .lang-flag-switch.en .circle {
        transform: translateX(0);
        background-image: url('https://upload.wikimedia.org/wikipedia/en/a/ae/Flag_of_the_United_Kingdom.svg');
      }
    `;
    document.head.appendChild(toggleStyle);

    setTimeout(() => {
      this.toggle = document.getElementById("langToggle");
      this.labelEN = document.getElementById("labelEN");
      this.labelJP = document.getElementById("labelJP");

      this.toggle?.addEventListener("click", () => {
        const newLang =
          localStorage.getItem("platonLang") === "en" ? "jp" : "en";
        setLang(newLang);
      });

      // Initial language setup
      setLang(currentLang);
    }, 300);

    const user = this.context.pageContext.user;
    const userInfoEl = document.getElementById("userInfo");
    if (userInfoEl) {
      userInfoEl.innerHTML = `<span style="margin-left: 10px;">${user.displayName}</span>`;
    }

    const userProfileEl = document.getElementById("userProfile");
    if (userProfileEl && user.email) {
      const imgUrl = `https://outlook.office365.com/owa/service.svc/s/GetPersonaPhoto?email=${encodeURIComponent(
        user.email
      )}&size=HR64x64`;
      const fallbackImg =
        "https://cdn-icons-png.flaticon.com/512/847/847969.png"; // fallback avatar image
      userProfileEl.innerHTML = `
        <img 
          src="${imgUrl}" 
          alt="${user.displayName}" 
          title="${user.displayName}" 
          class="user-avatar"
          onerror="this.onerror=null;this.src='${fallbackImg}';"
        />
      `;
    }

    const chatContainer = document.createElement("div");
    chatContainer.id = "chatContainer";
    chatContainer.className = "chat-container hidden";
    chatContainer.innerHTML = `
  <div class="chat-header">
    <span>${localeMap[currentLang as "en" | "jp"].ChatTitle}</span>
    <button id="chatCloseBtn" class="chat-close-button">&times;</button>
  </div>
  <iframe
    src="https://powerva.microsoft.com/webchat/bots?id=YOUR-BOT-ID-HERE"
    title="Chatbot"
    class="chatbot-frame"
    allow="microphone;"
  ></iframe>
  <div class="chat-input-container">
    <input
      type="text"
      id="chatInput"
      class="chat-input"
      placeholder="${localeMap[currentLang as "en" | "jp"].ChatPlaceholder}"
    />
    <button
      id="sendMessageBtn"
      class="send-button"
    >
      ${localeMap[currentLang as "en" | "jp"].SendButton}
    </button>
  </div>
`;
    document.body.appendChild(chatContainer);

    const chatLauncher = document.createElement("div");
    chatLauncher.className = "chat-launcher";
    chatLauncher.innerHTML = `
  <button id="chatToggleBtn" class="chat-toggle-button" title="Chat with us">
    <img src="https://corptb.sharepoint.com/sites/ProjectEngLand/SiteAssets/robo_advisor_robot_assistant_chatbot-512.webp" alt="Icon" style="width:30px; height:30px;">
  </button>
`;
    document.body.appendChild(chatLauncher);

    const chatStyle = document.createElement("style");
    chatStyle.textContent = `
  .chat-launcher {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 10001;
  }

  .chat-toggle-button {
    color: white;
  border: none;
  padding: 10px 16px;
  font-size: 14px;
  border-radius: 20px;
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
  }

  .chat-toggle-button:hover {
  background-color: #005a9e;
  color: white;
}

  .chat-container {
    position: fixed;
    bottom: 80px;
    right: 20px;
    width: 300px;
    height: 350px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 0 15px rgba(0,0,0,0.3);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    z-index: 10000;
  }

  .chat-container.hidden {
    display: none;
  }

  .chat-header {
    background-color: #0078d4;
    color: white;
    padding: 10px;
    font-size: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .chatbot-frame {
    flex: 1;
    border: none;
    width: 100%;
  }

  .chat-input-container {
    display: flex;
    padding: 10px;
    border-top: 1px solid #ccc;
    background: #f9f9f9;
  }

  .chat-input {
    width: 80%;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #ccc;
    font-size: 14px;
    margin-right: 10px;
  }

  .send-button {
    background-color: #0078d4;
    color: white;
    padding: 10px;
    border: none;
    border-radius: 20px;
    cursor: pointer;
  }

  .send-button:hover {
    background-color: #005a9e;
  }

  .chat-close-button {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
  }
`;
    document.head.appendChild(chatStyle);
    return Promise.resolve();
  }

  @override
  public onDispose(): void {
    const nav = document.getElementById("customTopNav");
    if (nav) {
      nav.remove();
    }

    const sideNav = document.querySelector(".custom-side-nav");
    if (sideNav) {
      sideNav.remove();
    }
  }
}

function applyTranslations(locale: "en" | "jp"): void {
  const strings = localeMap[locale];

  const labels = document.querySelectorAll("[data-i18n-key]");
  labels.forEach((label) => {
    const key = label.getAttribute("data-i18n-key");
    if (key && strings[key as keyof LocaleStrings]) {
      label.textContent = strings[key as keyof LocaleStrings];
    }
  });

  const footerPara = document.querySelector(".footer-left p");
  if (footerPara) footerPara.textContent = strings.ContactUs;

  const footerLink = document.querySelector(".footer-links a");
  if (footerLink) footerLink.textContent = strings.SupportChannel;

  const searchInput = document.getElementById(
    "spSearchInput"
  ) as HTMLInputElement;
  if (searchInput) searchInput.placeholder = strings.SearchPlaceholder;

  const chatHeader = document.querySelector(".chat-header span");
  if (chatHeader) chatHeader.textContent = strings.ChatTitle;

  const chatInput = document.getElementById("chatInput") as HTMLInputElement;
  if (chatInput) chatInput.placeholder = strings.ChatPlaceholder;

  const sendButton = document.getElementById("sendMessageBtn");
  if (sendButton) sendButton.textContent = strings.SendButton;

  // Optional: log that translations were applied
  console.log(`Applied ${locale} translations`);
}
