{"name": "platon-header", "version": "0.0.1", "private": true, "engines": {"node": ">=18.17.1 <19.0.0"}, "main": "lib/index.js", "scripts": {"build": "gulp bundle", "clean": "gulp clean", "test": "gulp test"}, "dependencies": {"@microsoft/decorators": "1.20.0", "@microsoft/sp-application-base": "1.20.0", "@microsoft/sp-core-library": "1.20.0", "@microsoft/sp-dialog": "1.20.0", "react-icons": "^5.5.0", "tslib": "2.3.1", "valibot": "^0.8.0"}, "devDependencies": {"@microsoft/eslint-config-spfx": "1.20.2", "@microsoft/eslint-plugin-spfx": "1.20.2", "@microsoft/rush-stack-compiler-4.7": "0.1.0", "@microsoft/sp-build-web": "1.20.2", "@microsoft/sp-module-interfaces": "1.20.2", "@rushstack/eslint-config": "4.0.1", "@types/webpack-env": "~1.15.2", "ajv": "^6.12.5", "eslint": "8.57.0", "gulp": "4.0.2", "typescript": "4.7.4"}}