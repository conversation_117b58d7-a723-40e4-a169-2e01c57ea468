{"$schema": "https://developer.microsoft.com/json-schemas/spfx-build/package-solution.schema.json", "solution": {"name": "platon-header-client-side-solution", "id": "1221e803-43cf-480f-9401-1deecb2f9829", "version": "*******", "includeClientSideAssets": true, "skipFeatureDeployment": true, "isDomainIsolated": false, "developer": {"name": "", "websiteUrl": "", "privacyUrl": "", "termsOfUseUrl": "", "mpnId": "Undefined-1.20.0"}, "metadata": {"shortDescription": {"default": "PlatonHeader description"}, "longDescription": {"default": "PlatonHeader description"}, "screenshotPaths": [], "videoUrl": "", "categories": []}, "features": [{"title": "Application Extension - Deployment of custom action", "description": "Deploys a custom action with ClientSideComponentId association", "id": "e4bc7103-fdeb-4799-9b9c-74607a251d3c", "version": "*******", "assets": {"elementManifests": ["elements.xml", "ClientSideInstance.xml"]}}]}, "paths": {"zippedPackage": "solution/platon-header.sppkg"}}