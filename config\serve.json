{"$schema": "https://developer.microsoft.com/json-schemas/spfx-build/spfx-serve.schema.json", "port": 4321, "https": true, "serveConfigurations": {"default": {"pageUrl": "https://corptb.sharepoint.com/sites/TMTMFUSOMIGRATION/SitePages/Platon.aspx", "customActions": {"98f4c649-a6f0-40cd-9540-deab3b49a7da": {"location": "ClientSideExtension.ApplicationCustomizer", "properties": {"testMessage": "Test message"}}}}, "platonSiteCustomization": {"pageUrl": "https://corptb.sharepoint.com/sites/TMTMFUSOMIGRATION/SitePages/Platon.aspx", "customActions": {"98f4c649-a6f0-40cd-9540-deab3b49a7da": {"location": "ClientSideExtension.ApplicationCustomizer", "properties": {"testMessage": "Test message"}}}}}}