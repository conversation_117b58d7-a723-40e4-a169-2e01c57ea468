declare interface IPlatonSiteCustomizationApplicationCustomizerStrings {
  Title: string;
  SearchPlaceholder: string;
  ContactUs?: string;
  SupportChannel?: string;
  ChatTitle?: string;
  ChatPlaceholder?: string;
  SendButton?: string;
  HomeLabel?: string;
  StartingPointLabel?: string;
  ShortTripsLabel?: string;
  DepthSessionLabel?: string;
  ModuleSeriesLabel?: string;
  ManualsLabel?: string;
  BlockTrainingLabel?: string;
  AccessPlatonLabel?: string;
  KeyUsersLabel?: string;
  PeakCaptureLabel?: string;
  ProjectEnglandLabel?: string;
  ContactUsLabel?: string;
}

declare module 'PlatonSiteCustomizationApplicationCustomizerStrings' {
  const strings: IPlatonSiteCustomizationApplicationCustomizerStrings;
  export = strings;
}

